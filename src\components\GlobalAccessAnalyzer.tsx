'use client';

import React, { useState, useEffect } from 'react';
import { Globe, Users, MapPin, BarChart3, TrendingUp, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface RegionAnalysis {
  region: string;
  avgLatency: number;
  successRate: number;
  nodeCount: number;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  userExperience: string;
}

interface GlobalAccessAnalyzerProps {
  target: string;
  isDarkMode: boolean;
  pingResults: any[];
}

const GlobalAccessAnalyzer: React.FC<GlobalAccessAnalyzerProps> = ({ target, isDarkMode, pingResults }) => {
  const [regionAnalysis, setRegionAnalysis] = useState<RegionAnalysis[]>([]);
  const [globalStats, setGlobalStats] = useState({
    totalNodes: 0,
    avgLatency: 0,
    successRate: 0,
    bestRegion: '',
    worstRegion: ''
  });
  const [selectedMetric, setSelectedMetric] = useState<'latency' | 'success' | 'quality'>('latency');

  // 分析全球访问质量
  const analyzeGlobalAccess = () => {
    if (!target || pingResults.length === 0) return;

    // 按地区分组分析
    const regionGroups = {
      '中国大陆': pingResults.filter(r => 
        r.location?.country === 'China' || r.location?.country === 'CN' || r.province
      ),
      '亚太地区': pingResults.filter(r => 
        r.location?.region && ['Asia', 'Eastern Asia', '亚洲'].includes(r.location.region) &&
        r.location?.country !== 'China' && r.location?.country !== 'CN'
      ),
      '北美地区': pingResults.filter(r => 
        r.location?.region && ['North America', '北美'].includes(r.location.region)
      ),
      '欧洲地区': pingResults.filter(r => 
        r.location?.region && ['Europe', '欧洲'].includes(r.location.region)
      ),
      '全球CDN': pingResults.filter(r => 
        r.testMethod === 'Cloudflare Workers' || r.testMethod === 'Globalping'
      )
    };

    const analysis: RegionAnalysis[] = [];

    Object.entries(regionGroups).forEach(([region, results]) => {
      if (results.length === 0) return;

      const successfulResults = results.filter(r => r.status === 'success');
      const avgLatency = successfulResults.length > 0 
        ? successfulResults.reduce((sum, r) => sum + r.ping, 0) / successfulResults.length 
        : 0;
      const successRate = (successfulResults.length / results.length) * 100;

      // 评估质量等级
      let quality: 'excellent' | 'good' | 'fair' | 'poor';
      let userExperience: string;

      if (avgLatency <= 50 && successRate >= 95) {
        quality = 'excellent';
        userExperience = '极佳 - 用户体验优秀';
      } else if (avgLatency <= 100 && successRate >= 90) {
        quality = 'good';
        userExperience = '良好 - 用户体验满意';
      } else if (avgLatency <= 200 && successRate >= 80) {
        quality = 'fair';
        userExperience = '一般 - 用户体验可接受';
      } else {
        quality = 'poor';
        userExperience = '较差 - 需要优化';
      }

      analysis.push({
        region,
        avgLatency: Math.round(avgLatency),
        successRate: Math.round(successRate),
        nodeCount: results.length,
        quality,
        userExperience
      });
    });

    // 计算全局统计
    const allSuccessful = pingResults.filter(r => r.status === 'success');
    const globalAvgLatency = allSuccessful.length > 0 
      ? allSuccessful.reduce((sum, r) => sum + r.ping, 0) / allSuccessful.length 
      : 0;
    const globalSuccessRate = (allSuccessful.length / pingResults.length) * 100;

    const sortedByLatency = analysis.filter(a => a.avgLatency > 0).sort((a, b) => a.avgLatency - b.avgLatency);
    const bestRegion = sortedByLatency[0]?.region || '';
    const worstRegion = sortedByLatency[sortedByLatency.length - 1]?.region || '';

    setRegionAnalysis(analysis);
    setGlobalStats({
      totalNodes: pingResults.length,
      avgLatency: Math.round(globalAvgLatency),
      successRate: Math.round(globalSuccessRate),
      bestRegion,
      worstRegion
    });
  };

  useEffect(() => {
    if (target && pingResults.length > 0) {
      analyzeGlobalAccess();
    }
  }, [target, pingResults]);

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 bg-green-100 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'fair': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case 'excellent': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'good': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'fair': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />;
    }
  };

  const getMetricValue = (region: RegionAnalysis, metric: string) => {
    switch (metric) {
      case 'latency': return `${region.avgLatency}ms`;
      case 'success': return `${region.successRate}%`;
      case 'quality': return region.quality;
      default: return '';
    }
  };

  const sortRegions = (regions: RegionAnalysis[], metric: string) => {
    switch (metric) {
      case 'latency': return regions.sort((a, b) => a.avgLatency - b.avgLatency);
      case 'success': return regions.sort((a, b) => b.successRate - a.successRate);
      case 'quality': 
        const qualityOrder = { excellent: 4, good: 3, fair: 2, poor: 1 };
        return regions.sort((a, b) => qualityOrder[b.quality] - qualityOrder[a.quality]);
      default: return regions;
    }
  };

  if (!target) {
    return (
      <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
        <div className="text-center">
          <Globe className={`mx-auto h-12 w-12 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mb-4`} />
          <h3 className={`text-lg font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
            全球访问质量分析
          </h3>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            输入网址并开始测试以查看全球访问质量
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      {/* 标题 */}
      <div className="flex items-center space-x-2 mb-6">
        <Globe className={`h-6 w-6 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          全球访问质量分析
        </h3>
      </div>

      {/* 全局统计 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center space-x-2 mb-1">
            <Users className="h-4 w-4 text-blue-500" />
            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              测试节点
            </span>
          </div>
          <div className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            {globalStats.totalNodes}
          </div>
        </div>

        <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center space-x-2 mb-1">
            <Clock className="h-4 w-4 text-orange-500" />
            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              平均延迟
            </span>
          </div>
          <div className="text-lg font-bold text-orange-500">
            {globalStats.avgLatency}ms
          </div>
        </div>

        <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center space-x-2 mb-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              成功率
            </span>
          </div>
          <div className="text-lg font-bold text-green-500">
            {globalStats.successRate}%
          </div>
        </div>

        <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center space-x-2 mb-1">
            <TrendingUp className="h-4 w-4 text-purple-500" />
            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              最佳地区
            </span>
          </div>
          <div className={`text-sm font-bold text-purple-500 ${isDarkMode ? 'text-purple-400' : ''}`}>
            {globalStats.bestRegion || '无数据'}
          </div>
        </div>
      </div>

      {/* 排序选项 */}
      <div className="flex space-x-2 mb-4">
        {[
          { key: 'latency', label: '按延迟排序', icon: Clock },
          { key: 'success', label: '按成功率排序', icon: CheckCircle },
          { key: 'quality', label: '按质量排序', icon: BarChart3 }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setSelectedMetric(key as any)}
            className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedMetric === key
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icon className="h-4 w-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* 地区分析列表 */}
      <div className="space-y-3">
        {sortRegions([...regionAnalysis], selectedMetric).map((region, index) => (
          <div key={region.region} className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <MapPin className={`h-4 w-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`} />
                <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {region.region}
                </span>
                <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  ({region.nodeCount} 节点)
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                {getQualityIcon(region.quality)}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getQualityColor(region.quality)} ${isDarkMode ? 'bg-opacity-20' : ''}`}>
                  {region.quality === 'excellent' ? '优秀' : 
                   region.quality === 'good' ? '良好' : 
                   region.quality === 'fair' ? '一般' : '较差'}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className={`font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>平均延迟：</span>
                <span className={`ml-1 font-semibold ${region.avgLatency <= 100 ? 'text-green-500' : region.avgLatency <= 200 ? 'text-yellow-500' : 'text-red-500'}`}>
                  {region.avgLatency}ms
                </span>
              </div>
              <div>
                <span className={`font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>成功率：</span>
                <span className={`ml-1 font-semibold ${region.successRate >= 90 ? 'text-green-500' : region.successRate >= 70 ? 'text-yellow-500' : 'text-red-500'}`}>
                  {region.successRate}%
                </span>
              </div>
              <div>
                <span className={`font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>用户体验：</span>
                <span className={`ml-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {region.userExperience}
                </span>
              </div>
            </div>
          </div>
        ))}
        
        {regionAnalysis.length === 0 && (
          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <BarChart3 className="mx-auto h-8 w-8 mb-2" />
            <p>等待测试数据...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GlobalAccessAnalyzer;
