'use client';

import React, { useState } from 'react';

export default function TestPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    const tests = [
      {
        name: '健康检查',
        url: '/api/health',
        method: 'GET'
      },
      {
        name: '配置检查',
        url: '/api/config',
        method: 'GET'
      },
      {
        name: 'Ping测试 - 百度',
        url: '/api/ping-cloudping',
        method: 'POST',
        body: { target: 'https://www.baidu.com' }
      },
      {
        name: 'Ping测试 - GET方法',
        url: '/api/ping-cloudping?target=https://www.baidu.com&maxNodes=10',
        method: 'GET'
      },
      {
        name: '全球节点测试',
        url: '/api/ping-global',
        method: 'POST',
        body: { target: 'https://www.google.com', maxNodes: 20 }
      },
      {
        name: 'Vercel Edge测试',
        url: '/api/ping-vercel-edge',
        method: 'POST',
        body: { target: 'https://www.baidu.com' }
      },
      {
        name: '访问统计',
        url: '/api/visit-stats',
        method: 'POST',
        body: {}
      },
      {
        name: 'Redis连接测试',
        url: '/api/test-redis',
        method: 'GET'
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const response = await fetch(test.url, {
          method: test.method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: test.body ? JSON.stringify(test.body) : undefined,
        });

        const endTime = Date.now();
        const duration = endTime - startTime;
        
        const result = {
          name: test.name,
          status: response.ok ? 'success' : 'error',
          statusCode: response.status,
          duration: `${duration}ms`,
          data: response.ok ? await response.json() : await response.text(),
        };

        setTestResults(prev => [...prev, result]);
      } catch (error) {
        setTestResults(prev => [...prev, {
          name: test.name,
          status: 'error',
          statusCode: 'N/A',
          duration: 'N/A',
          data: error instanceof Error ? error.message : 'Unknown error',
        }]);
      }

      // 添加小延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🧪 API功能测试</h1>
        
        <div className="mb-8">
          <button
            onClick={runTests}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-6 py-3 rounded-lg font-medium transition-colors"
          >
            {isLoading ? '测试中...' : '开始测试'}
          </button>
        </div>

        <div className="space-y-4">
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                result.status === 'success'
                  ? 'bg-green-900/20 border-green-500'
                  : 'bg-red-900/20 border-red-500'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-medium">{result.name}</h3>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-sm ${
                    result.status === 'success'
                      ? 'bg-green-600 text-white'
                      : 'bg-red-600 text-white'
                  }`}>
                    {result.status}
                  </span>
                  <span className="text-gray-400 text-sm">
                    {result.statusCode}
                  </span>
                  <span className="text-gray-400 text-sm">
                    {result.duration}
                  </span>
                </div>
              </div>
              
              <details className="mt-2">
                <summary className="cursor-pointer text-gray-300 hover:text-white">
                  查看响应数据
                </summary>
                <pre className="mt-2 p-3 bg-gray-800 rounded text-sm overflow-auto max-h-64">
                  {typeof result.data === 'object' 
                    ? JSON.stringify(result.data, null, 2)
                    : result.data
                  }
                </pre>
              </details>
            </div>
          ))}
        </div>

        {isLoading && (
          <div className="mt-8 text-center">
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>正在执行测试...</span>
            </div>
          </div>
        )}

        <div className="mt-12 p-6 bg-gray-800 rounded-lg">
          <h2 className="text-xl font-bold mb-4">📋 测试说明</h2>
          <ul className="space-y-2 text-gray-300">
            <li>• <strong>健康检查</strong>: 验证服务器基本状态</li>
            <li>• <strong>配置检查</strong>: 验证环境变量和配置</li>
            <li>• <strong>Ping测试</strong>: 测试多云网络延迟功能</li>
            <li>• <strong>快速连通性测试</strong>: 测试基础网络连通性</li>
            <li>• <strong>访问统计</strong>: 测试访问计数功能</li>
          </ul>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/"
            className="inline-block bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg font-medium transition-colors"
          >
            ← 返回主页
          </a>
        </div>
      </div>
    </div>
  );
}
