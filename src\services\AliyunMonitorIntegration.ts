// 监控API集成 - 简化版本 (移除阿里云依赖)

// 简化的监控集成类
export class AliyunMonitorIntegration {
  constructor() {
    // 不再依赖阿里云配置
  }

  // 检查是否已配置 - 始终返回false，因为我们不再使用阿里云
  isConfigured(): boolean {
    return false; // 禁用阿里云监控
  }

  // 获取网站统计数据 - 返回空数据
  async getWebsiteStats(url: string) {
    return {
      success: false,
      error: '阿里云监控已禁用',
      data: null
    };
  }

  // 创建站点监控任务 - 返回未配置错误
  async createSiteMonitor(url: string, taskName: string) {
    return {
      success: false,
      error: '阿里云监控未配置',
      taskId: null
    };
  }

  // 获取监控数据 - 返回空数据
  async getMonitorData(taskId: string, startTime: string, endTime: string) {
    return {
      success: false,
      error: '阿里云监控已禁用',
      data: []
    };
  }
}

// 创建单例实例
export const aliyunMonitor = new AliyunMonitorIntegration();

// 增强版ping测试函数 - 简化版本
export async function enhancedPingTest(target: string) {
  try {
    // 使用多云API进行测试
    const response = await fetch('/api/ping-cloudflare-worker', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target })
    });
    
    const results = await response.json();

    return {
      success: true,
      realTime: results,
      historical: null, // 不再使用阿里云历史数据
      enhanced: false   // 增强功能已禁用
    };
  } catch (error) {
    // 增强ping测试失败
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

export default AliyunMonitorIntegration;
