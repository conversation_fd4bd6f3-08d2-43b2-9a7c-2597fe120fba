import type { NextApiRequest, NextApiResponse } from 'next';

/**
 * CORS中间件 - 统一处理跨域请求
 */
export function setCorsHeaders(res: NextApiResponse) {
  // 设置CORS头部
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24小时预检缓存
}

/**
 * 处理预检请求
 */
export function handleCorsPreflightRequest(req: NextApiRequest, res: NextApiResponse): boolean {
  if (req.method === 'OPTIONS') {
    setCorsHeaders(res);
    res.status(200).end();
    return true; // 表示已处理预检请求
  }
  return false; // 表示不是预检请求，继续处理
}

/**
 * CORS装饰器 - 自动为API路由添加CORS支持
 */
export function withCors(handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void> | void) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // 设置CORS头部
    setCorsHeaders(res);
    
    // 处理预检请求
    if (handleCorsPreflightRequest(req, res)) {
      return;
    }
    
    try {
      // 执行原始处理函数
      await handler(req, res);
    } catch (error) {
      // API Error
      res.status(500).json({ 
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}

/**
 * 安全的CORS配置 - 用于生产环境
 */
export function setSecureCorsHeaders(res: NextApiResponse, allowedOrigins: string[] = []) {
  const defaultAllowedOrigins = [
    'https://localhost:3000',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002'
  ];

  const origins = allowedOrigins.length > 0 ? allowedOrigins : defaultAllowedOrigins;

  res.setHeader('Access-Control-Allow-Origin', origins.join(', '));
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}
