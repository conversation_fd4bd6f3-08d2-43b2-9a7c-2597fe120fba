import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 🔧 使用 Vercel Redis (Upstash) 配置
    const kvRestApiToken = process.env.KV_REST_API_TOKEN
    const kvUrl = process.env.KV_URL || process.env.REDIS_URL

    if (!kvRestApiToken || !kvUrl) {
      return res.status(400).json({
        error: 'Redis configuration not found',
        message: 'Please configure KV_REST_API_TOKEN and KV_URL environment variables'
      })
    }

    // 构建 Upstash Redis REST API 端点
    // 优先使用配置的 REST API URL，否则从连接字符串构建
    const restApiUrl = process.env.KV_REST_API_URL || (() => {
      const redisUrl = new URL(kvUrl)
      return `https://${redisUrl.hostname.replace(':6379', '')}`
    })()

    const { initialCount = 1267 } = req.body

    // 检查当前计数是否存在
    const getResponse = await fetch(`${restApiUrl}/get/visit_count`, {
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(5000)
    })

    let currentCount = initialCount
    if (getResponse.ok) {
      const getData = await getResponse.json()
      if (getData.result) {
        currentCount = getData.result
      }
    }

    // 如果没有现有计数，设置初始值
    if (!getResponse.ok || !currentCount || currentCount < initialCount) {
      const setResponse = await fetch(`${restApiUrl}/set/visit_count/${initialCount}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${kvRestApiToken}`,
        },
        signal: AbortSignal.timeout(5000)
      })

      if (!setResponse.ok) {
        throw new Error(`Failed to initialize visit count: ${setResponse.status}`)
      }

      currentCount = initialCount
    }

    // 设置最后访问时间
    const timestamp = new Date().toISOString()
    await fetch(`${restApiUrl}/set/last_visit/${timestamp}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(3000)
    }).catch(() => {}) // 忽略错误

    res.status(200).json({
      success: true,
      visitCount: currentCount,
      lastVisit: timestamp,
      initialized: true,
      source: 'redis'
    })

  } catch (error) {
    res.status(500).json({
      error: 'Failed to initialize Redis visit stats',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
