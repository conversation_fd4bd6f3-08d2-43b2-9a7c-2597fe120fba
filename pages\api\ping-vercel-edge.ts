import type { NextApiRequest, NextApiResponse } from 'next'
import { withCors } from '../../src/utils/cors'

const handler = async function(req: NextApiRequest, res: NextApiResponse) {
  // 支持GET和POST请求
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 支持GET和POST请求的参数获取
    const params = req.method === 'GET' ? req.query : req.body
    const { target, url } = params
    const testUrl = target || url

    if (!testUrl) {
      return res.status(400).json({ error: 'Target URL is required' })
    }

    const startTime = Date.now()
    
    // 使用Vercel Edge Function进行ping测试
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; PingBot/1.0)',
      },
    });

    clearTimeout(timeoutId);
    
    const endTime = Date.now()
    const latency = endTime - startTime

    res.status(200).json({
      success: true,
      latency,
      status: response.status,
      timestamp: new Date().toISOString(),
      region: process.env.VERCEL_REGION || 'hkg1',
      availableRegions: (process.env.VERCEL_EDGE_REGIONS || 'hkg1,sin1,icn1,hnd1').split(','),
      preferredRegions: process.env.VERCEL_EDGE_REGIONS || 'hkg1,sin1,icn1,hnd1'
    })
  } catch (error) {
    let errorMessage = 'Unknown error';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout';
        statusCode = 408;
      } else if (error.message.includes('timeout')) {
        errorMessage = 'The operation was aborted due to timeout';
        statusCode = 408;
      } else {
        errorMessage = error.message;
      }
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString(),
      region: process.env.VERCEL_REGION || 'hkg1'
    })
  }
}

export default withCors(handler)
