import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 返回前端需要的配置信息（不包含敏感信息）
    const config = {
      // 多云测试配置
      enableMultiCloudTesting: process.env.ENABLE_MULTI_CLOUD_TESTING === 'true',
      
      // Cloudflare Workers 配置
      cloudflareWorkerUrl: process.env.CLOUDFLARE_WORKER_URL || 'https://ping-api.wobys.dpdns.org',
      cloudflarePreferredRegions: (process.env.CLOUDFLARE_PREFERRED_REGIONS || 'SHA,HKG,TPE,NRT,ICN,SIN').split(','),
      
      // Vercel Edge 配置
      vercelEdgeRegions: (process.env.VERCEL_EDGE_REGIONS || 'hkg1,sin1,icn1,hnd1').split(','),
      
      // Redis 配置状态（不暴露实际连接信息）
      redisConfigured: !!(process.env.KV_URL && process.env.KV_REST_API_TOKEN),
      
      // UptimeRobot 配置状态
      uptimeRobotConfigured: !!process.env.UPTIMEROBOT_API_KEY,

      // 新监控平台配置
      freshpingConfigured: !!process.env.FRESHPING_API_KEY,
      hetrixToolsConfigured: !!process.env.HETRIXTOOLS_API_KEY,
      statusCakeConfigured: !!process.env.STATUSCAKE_API_KEY,
      uptimeRobotConfigured: !!process.env.UPTIMEROBOT_API_KEY,
      site24x7Configured: !!process.env.SITE24X7_API_KEY,
      betterUptimeConfigured: !!process.env.BETTERUPTIME_API_KEY,
      checklyConfigured: !!process.env.CHECKLY_API_KEY,

      // 平台优先级配置 - 完整的12个平台
      platformPriority: {
        primary: 'itdog',              // 主要: ITDOG.CN
        backup: ['17ce', 'chinaz'],    // 中国备用
        international: ['freshping', 'hetrixtools', 'statuscake', 'uptimerobot', 'site24x7', 'betteruptime', 'checkly'], // 国际平台
        fallback: ['ping-pe', 'multi-platform']   // 降级
      },

      // 功能开关
      features: {
        visitCounter: !!(process.env.KV_URL && process.env.KV_REST_API_TOKEN),
        uptimeRobotIntegration: !!process.env.UPTIMEROBOT_API_KEY,
        freshpingIntegration: !!process.env.FRESHPING_API_KEY,
        hetrixToolsIntegration: !!process.env.HETRIXTOOLS_API_KEY,
        statusCakeIntegration: !!process.env.STATUSCAKE_API_KEY,
        site24x7Integration: !!process.env.SITE24X7_API_KEY,
        betterUptimeIntegration: !!process.env.BETTERUPTIME_API_KEY,
        checklyIntegration: !!process.env.CHECKLY_API_KEY,
        multiCloudTesting: process.env.ENABLE_MULTI_CLOUD_TESTING === 'true',
        cloudflareWorkers: !!process.env.CLOUDFLARE_WORKER_URL,
        vercelEdgeFunctions: true, // 总是可用
        itdogPrimary: true, // ITDOG作为主力平台
        comprehensiveTesting: true // 全平台测试功能
      }
    }

    res.status(200).json({
      success: true,
      config
    })

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to load configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
