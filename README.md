# 🌐 Ping Network Monitor

<div align="center">

![Ping Network Monitor](https://img.shields.io/badge/Ping-Network%20Monitor-blue?style=for-the-badge&logo=network&logoColor=white)
![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue?style=for-the-badge&logo=typescript&logoColor=white)
![Vercel](https://img.shields.io/badge/Vercel-Deployed-black?style=for-the-badge&logo=vercel&logoColor=white)

**现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控**

[🚀 在线体验](https://ping-qw00jqpqy-wob21s-projects.vercel.app) • [📖 文档](#-功能特性) • [🛠️ 部署指南](#-快速部署)

</div>

---

## ✨ 功能特性

### 🎯 **智能网站分类**
- **🏠 国内网站识别** - 智能识别中国大陆网站，显示绿色低延迟
- **🌍 国外网站检测** - 准确识别海外网站，显示橙色中等延迟  
- **🚫 被墙网站标记** - 自动检测被GFW屏蔽的网站，显示红色高延迟
- **🧠 AI智能判断** - 基于500ms阈值的动态分类算法

### 🗺️ **中国地图可视化**
- **📍 34个省级行政区** - 完整覆盖中国大陆、港澳台地区
- **🎨 实时延迟着色** - 根据网络延迟动态调整地图颜色
- **📊 省份网格视图** - 紧凑的网格布局显示各省延迟数据
- **🌙 深色模式支持** - 优雅的深色主题界面

### ⚡ **多云并发测试**
- **☁️ Vercel Edge Functions** - 全球边缘节点加速测试
- **🔥 Cloudflare Workers** - 200+数据中心并发测试
- **🔄 智能降级策略** - 云服务→客户端→模拟数据三级降级
- **⚡ 毫秒级响应** - 优化的网络测试算法

### 📈 **实时监控面板**
- **📊 访问统计** - Redis持久化的访问计数器
- **🔍 网络质量分析** - 延迟、丢包率、抖动等指标
- **📱 响应式设计** - 完美适配桌面端和移动端
- **🎛️ 多视图切换** - 地图、网格、监控多种视图模式

---

## 🚀 快速部署

### 📋 **环境要求**
- Node.js 18.0+
- npm 或 yarn
- Vercel 账户（推荐）

### ⚙️ **环境变量配置**

```bash
# Redis 配置 (Upstash)
KV_REST_API_TOKEN=your_redis_token
KV_URL=your_redis_url

# Vercel Edge 配置
VERCEL_EDGE_REGIONS=hkg1,sin1,icn1,hnd1

# Cloudflare Worker 配置
CLOUDFLARE_WORKER_URL=https://your-worker.your-domain.workers.dev
CLOUDFLARE_PREFERRED_REGIONS=SHA,HKG,TPE,NRT,ICN,SIN

# UptimeRobot 配置
UPTIMEROBOT_API_KEY=your_uptimerobot_key
```

### 🛠️ **本地开发**

```bash
# 克隆项目
git clone https://github.com/wob-21/ping.git
cd ping

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

### 🌐 **一键部署到 Vercel**

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/wob-21/ping)

---

## 🏗️ 技术架构

### 🎨 **前端技术栈**
```
Next.js 15.3.5    │ React 全栈框架
TypeScript        │ 类型安全的 JavaScript
Tailwind CSS      │ 原子化 CSS 框架
ECharts           │ 数据可视化图表库
Lucide React      │ 现代化图标库
```

### ⚡ **后端服务**
```
Vercel Edge       │ 全球边缘计算节点
Cloudflare Workers│ 无服务器计算平台
Upstash Redis     │ 无服务器 Redis 数据库
UptimeRobot API   │ 网站监控服务
```

### 🔧 **核心算法**
- **智能网站分类** - 基于域名白名单、TLD识别、延迟阈值的多层判断
- **延迟校准算法** - 地理位置感知的延迟数据校正
- **稳定性平滑** - 减少网络波动对测试结果的影响
- **数据补全机制** - 确保34个省级行政区数据完整性

---

## 📊 API 接口

### 🔍 **网络测试 API**

```typescript
// GET /api/ping-vercel-edge
{
  "results": [
    {
      "province": "北京",
      "city": "北京",
      "latency": 45,
      "status": "success"
    }
  ],
  "metadata": {
    "testType": "vercel-edge",
    "timestamp": 1703123456789
  }
}
```

### 📈 **访问统计 API**

```typescript
// GET /api/visit-stats
{
  "visits": 1267,
  "lastVisit": "2024-01-01T00:00:00.000Z",
  "source": "redis",
  "success": true
}
```

---

## 🎯 网站分类算法

### 🧠 **智能判断流程**

```mermaid
graph TD
    A[输入网站URL] --> B{国内网站白名单?}
    B -->|是| C[🏠 国内网站]
    B -->|否| D{国外网站列表?}
    D -->|是| E[🌍 国外网站]
    D -->|否| F{被墙网站列表?}
    F -->|是| G[🚫 被墙网站]
    F -->|否| H{中国域名后缀?}
    H -->|是| C
    H -->|否| I[延迟测试]
    I --> J{延迟 < 500ms?}
    J -->|是| C
    J -->|否| E
```

### 📋 **分类标准**

| 类型 | 延迟范围 | 颜色标识 | 示例网站 |
|------|----------|----------|----------|
| 🏠 国内网站 | 50-450ms | 🟢 绿色 | baidu.com, qq.com |
| 🌍 国外网站 | 500-1000ms | 🟠 橙色 | github.com, stackoverflow.com |
| 🚫 被墙网站 | >1000ms | 🔴 红色 | google.com, facebook.com |

---

## 🌟 特色功能

### 🗺️ **中国地图可视化**
- 基于 ECharts 的交互式地图
- 实时延迟数据着色
- 支持省份点击查看详情
- 响应式布局适配

### ⚡ **多云测试架构**
- Vercel Edge Functions 亚太优化
- Cloudflare Workers 全球覆盖
- 智能路由选择最优节点
- 并发测试提升速度

### 📊 **实时监控**
- Redis 持久化访问统计
- WebRTC P2P 延迟测试
- 网络质量综合评估
- 历史数据趋势分析

---

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 🐛 **报告问题**
- 使用 [GitHub Issues](https://github.com/wob-21/ping/issues) 报告 Bug
- 提供详细的复现步骤和环境信息

### 💡 **功能建议**
- 在 Issues 中提出新功能建议
- 描述使用场景和预期效果

### 🔧 **代码贡献**
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

---

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 LICENSE 文件了解详情

---

## 🙏 致谢

- [Next.js](https://nextjs.org/) - 强大的 React 框架
- [Vercel](https://vercel.com/) - 优秀的部署平台
- [Cloudflare](https://cloudflare.com/) - 全球 CDN 服务
- [ECharts](https://echarts.apache.org/) - 专业的数据可视化库
- [Tailwind CSS](https://tailwindcss.com/) - 实用的 CSS 框架

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**

Made with ❤️ by [wob](https://github.com/wob-21)

</div>

---

## 🔧 高级配置

### 🌐 **多云部署配置**

#### Vercel 配置
```json
{
  "functions": {
    "pages/api/**/*.ts": {
      "runtime": "nodejs18.x"
    }
  },
  "regions": ["hkg1", "sin1", "icn1", "hnd1"]
}
```

#### Cloudflare Workers 配置
```toml
name = "ping-api"
main = "api/ping-cloudflare-worker.js"
compatibility_date = "2024-01-01"

[vars]
SUPPORTED_REGIONS = "SHA,BJS,HKG,TPE,NRT,ICN,SIN"
MAX_TIMEOUT = "8000"
```

### 📊 **监控配置**

#### Redis 持久化
```typescript
// 访问统计持久化
const visitStats = {
  visits: number,
  lastVisit: string,
  lastUpdated: number,
  source: 'redis' | 'mock'
}
```

#### 性能监控
```typescript
// 网络质量指标
const networkMetrics = {
  latency: number,      // 延迟 (ms)
  jitter: number,       // 抖动 (ms)
  packetLoss: number,   // 丢包率 (%)
  bandwidth: number,    // 带宽 (Mbps)
  uptime: number        // 可用性 (%)
}
```

---

## 🎨 界面预览

### 🖥️ **桌面端界面**
```
┌─────────────────────────────────────────────────────────────┐
│  🌐 Ping工具 - 网络连通性测试                    访问次数: 1,267 │
├─────────────────────────────────────────────────────────────┤
│  https://wobshare.us.kg/                           [开始] │
├─────────────────────────────────────────────────────────────┤
│  [地图] [网格] [监控] [CDN] [全球]                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           🗺️ 中国网络延迟地图                                │
│                                                             │
│     ┌─────────────────────────────────────────────┐         │
│     │  🟢 北京 45ms    🟡 上海 78ms                │         │
│     │  🟢 广东 52ms    🟠 新疆 234ms               │         │
│     │  🟢 浙江 61ms    🟠 西藏 198ms               │         │
│     └─────────────────────────────────────────────┘         │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│  ⚡ 增强测试结果                    📊 节点测试结果           │
│  延迟: 16ms                        北京: 30ms               │
│  可用性: 100%                      上海: 50ms               │
│  丢包率: 0%                        广州: 45ms               │
└─────────────────────────────────────────────────────────────┘
```

### 📱 **移动端适配**
- 响应式布局自动适配
- 触摸友好的交互设计
- 优化的移动端地图显示
- 简化的功能面板

---

## 🔍 故障排除

### ❓ **常见问题**

#### Q: API 返回 405 错误
**A:** 检查 Vercel 部署配置，确保没有启用静态导出模式
```bash
# 检查 next.config.ts
output: 'export' // ❌ 删除此配置
```

#### Q: 访问统计不持久化
**A:** 配置 Redis 环境变量
```bash
KV_REST_API_TOKEN=your_token
KV_URL=your_redis_url
```

#### Q: 延迟数据不准确
**A:** 调整延迟阈值配置
```typescript
// 当前阈值: 500ms
const threshold = 500; // 可调整为 300-800ms
```

### 🐛 **调试模式**

启用详细日志：
```bash
# 开发环境
DEBUG=ping:* npm run dev

# 生产环境查看 Vercel 函数日志
vercel logs
```

---

## 📈 性能优化

### ⚡ **加载速度优化**
- Next.js 静态生成 (SSG)
- 图片懒加载和优化
- 代码分割和按需加载
- CDN 加速静态资源

### 🔄 **缓存策略**
- Redis 缓存访问统计
- 浏览器缓存静态资源
- API 响应缓存 (60秒)
- 智能缓存失效机制

### 📊 **监控指标**
- Core Web Vitals 监控
- API 响应时间追踪
- 错误率统计分析
- 用户体验指标

---

## 🌍 国际化支持

### 🗣️ **多语言计划**
- 🇨🇳 简体中文 (当前)
- 🇺🇸 English (计划中)
- 🇯🇵 日本語 (计划中)
- 🇰🇷 한국어 (计划中)

### 🌐 **地区适配**
- 中国大陆优化版本
- 国际版本 (去除敏感功能)
- 地区特定的网站列表
- 本地化的延迟阈值

---

## 🔮 未来规划

### 🚀 **v2.0 路线图**
- [ ] 🤖 AI 智能网络诊断
- [ ] 📊 高级数据分析面板
- [ ] 🔔 实时告警通知系统
- [ ] 📱 移动端 PWA 应用
- [ ] 🌐 多地区部署支持
- [ ] 🔐 用户账户系统
- [ ] 📈 历史数据导出
- [ ] 🎨 自定义主题系统

### 💡 **创新功能**
- WebRTC P2P 延迟测试
- 区块链节点连通性测试
- 5G/6G 网络质量评估
- 物联网设备网络监控

---

<div align="center">

## 🌟 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=wob-21/ping&type=Date)](https://star-history.com/#wob-21/ping&Date)

**感谢每一位 Star 的朋友！您的支持是我们前进的动力！**

</div>
